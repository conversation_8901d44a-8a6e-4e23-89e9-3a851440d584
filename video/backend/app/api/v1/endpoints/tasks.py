"""
任务管理API端点
"""

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.core.dependencies import (
    get_current_active_user, require_task_create, require_task_read,
    require_task_update, require_task_delete, RequireOwnership
)
from app.models.task import Task, Video
from app.models.user import User
from app.services.task_service import TaskService
from app.core.config import settings
from app.tasks.video_tasks import process_task_videos
import json
import os
import uuid
import shutil
from pathlib import Path

router = APIRouter()


@router.get("", response_model=List[dict])
async def get_tasks(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_task_read)
):
    """获取任务列表"""
    tasks = db.query(Task).order_by(Task.created_at.desc()).all()
    return [
        {
            "id": task.id,
            "name": task.name,
            "description": task.description,
            "status": task.status,
            "progress": task.progress,
            "created_at": task.created_at.isoformat(),
            "updated_at": task.updated_at.isoformat(),
            "video_count": len(task.videos)
        }
        for task in tasks
    ]


@router.post("", response_model=dict)
async def create_task(
    name: str = Form(...),
    description: str = Form(""),
    config: str = Form("{}"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_task_create)
):
    """创建新任务"""
    try:
        config_dict = json.loads(config) if config else {}
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid config JSON")

    task = Task(
        name=name,
        description=description,
        config=config_dict,
        status="pending"
    )

    db.add(task)
    db.commit()
    db.refresh(task)

    return {
        "id": task.id,
        "name": task.name,
        "description": task.description,
        "status": task.status,
        "created_at": task.created_at.isoformat()
    }


@router.get("/{task_id}", response_model=dict)
async def get_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_task_read)
):
    """获取任务详情"""
    from app.models.task import AnalysisResult

    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 构建视频信息，包含分析进度和摘要
    videos_data = []
    for video in task.videos:
        # 获取视频的分析结果
        analysis_results = db.query(AnalysisResult).filter(
            AnalysisResult.video_id == video.id
        ).all()

        # 计算分析进度
        analysis_progress = 0.0
        analysis_summary = None

        if analysis_results:
            # 根据分析步骤计算进度
            completed_steps = len(analysis_results)
            total_steps = 3  # basic_info, content_analysis, plot_analysis
            analysis_progress = min((completed_steps / total_steps) * 100, 100)

            # 从content_analysis结果中提取摘要信息
            for result in analysis_results:
                if result.step == "content_analysis" and result.result:
                    content_result = result.result
                    analysis_summary = {
                        "scene_count": len(content_result.get("scenes", [])),
                        "character_count": len(content_result.get("characters", [])),
                        "clip_count": len(content_result.get("objects", []))
                    }
                    break

        videos_data.append({
            "id": video.id,
            "filename": video.filename,
            "original_filename": video.original_filename,
            "duration": video.duration,
            "resolution": video.resolution,
            "fps": video.fps,
            "file_size": video.file_size,
            "thumbnail": f"/api/v1/videos/{video.id}/thumbnail" if video.key_frame_thumbnail_id else None,
            "status": video.status,
            "analysis_progress": analysis_progress,
            "analysis_summary": analysis_summary,
            "created_at": video.created_at.isoformat()
        })

    return {
        "id": task.id,
        "name": task.name,
        "description": task.description,
        "status": task.status,
        "progress": task.progress,
        "config": task.config,
        "created_at": task.created_at.isoformat(),
        "updated_at": task.updated_at.isoformat(),
        "videos": videos_data
    }


@router.put("/{task_id}", response_model=dict)
async def update_task(
    task_id: int,
    name: str = None,
    description: str = None,
    status: str = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_task_update)
):
    """更新任务"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    if name is not None:
        task.name = name
    if description is not None:
        task.description = description
    if status is not None:
        task.status = status

    db.commit()
    db.refresh(task)

    return {
        "id": task.id,
        "name": task.name,
        "description": task.description,
        "status": task.status,
        "updated_at": task.updated_at.isoformat()
    }


@router.delete("/{task_id}")
async def delete_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_task_delete)
):
    """删除任务"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 手动处理循环依赖：先清除所有video的key_frame_thumbnail_id引用
    for video in task.videos:
        if video.key_frame_thumbnail_id:
            video.key_frame_thumbnail_id = None

    # 提交清除引用的更改
    db.commit()

    # 现在可以安全删除任务（会级联删除videos和frames）
    db.delete(task)
    db.commit()

    return {"message": "Task deleted successfully"}


@router.post("/{task_id}/upload")
async def upload_video(
    task_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_task_update)
):
    """上传视频到任务"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 检查文件类型
    if not file.filename:
        raise HTTPException(status_code=400, detail="No filename provided")

    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in settings.VIDEO_FORMATS:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file format. Supported formats: {', '.join(settings.VIDEO_FORMATS)}"
        )

    # 检查文件大小
    file_size = 0
    if hasattr(file, 'size') and file.size:
        file_size = file.size
        if file_size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE // (1024*1024*1024)}GB"
            )

    try:
        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4()}{file_ext}"
        file_path = os.path.join(settings.UPLOAD_DIR, unique_filename)

        # 确保上传目录存在
        os.makedirs(settings.UPLOAD_DIR, exist_ok=True)

        # 保存文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # 获取实际文件大小
        if file_size == 0:
            file_size = os.path.getsize(file_path)

        # 创建Video记录
        video = Video(
            task_id=task_id,
            filename=unique_filename,
            original_filename=file.filename,
            file_path=file_path,
            file_size=file_size,
            status="uploaded"
        )

        db.add(video)

        # 检查并更新任务状态
        # 如果任务不是处于活跃状态（pending或processing），则重置为pending
        if task.status not in ["pending", "processing"]:
            task.status = "pending"
            task.progress = 0.0  # 重置进度
            task.celery_task_id = None  # 清除之前的任务ID

        db.commit()
        db.refresh(video)
        db.refresh(task)

        return {
            "id": video.id,
            "message": "Video uploaded successfully",
            "filename": video.filename,
            "original_filename": video.original_filename,
            "size": video.file_size,
            "status": video.status,
            "task_status": task.status  # 返回更新后的任务状态
        }

    except Exception as e:
        # 如果出错，删除已上传的文件
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


@router.post("/{task_id}/start")
async def start_task_processing(task_id: int, db: Session = Depends(get_db)):
    """开始任务处理"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 检查任务状态
    if task.status not in ["pending", "paused"]:
        raise HTTPException(
            status_code=400,
            detail=f"Task cannot be started. Current status: {task.status}"
        )

    # 检查是否有视频
    video_count = db.query(Video).filter(Video.task_id == task_id).count()
    if video_count == 0:
        raise HTTPException(
            status_code=400,
            detail="请先上传视频到任务"
        )

    celery_task = process_task_videos.delay(task_id)

    # 更新任务状态，保存Celery任务ID
    task.status = "processing"
    task.progress = 0.0
    task.celery_task_id = celery_task.id
    db.commit()
    db.refresh(task)

    return {
        "id": task.id,
        "status": task.status,
        "progress": task.progress,
        "video_count": video_count,
        "celery_task_id": celery_task.id,
        "message": "Task processing started"
    }


@router.post("/{task_id}/pause")
async def pause_task_processing(task_id: int, db: Session = Depends(get_db)):
    """暂停任务处理"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 检查任务状态
    if task.status != "processing":
        raise HTTPException(
            status_code=400,
            detail=f"Task cannot be paused. Current status: {task.status}"
        )

    # 如果有Celery任务ID，尝试撤销任务
    if task.celery_task_id:
        try:
            from app.tasks.celery import celery
            celery.control.revoke(task.celery_task_id, terminate=True)
        except Exception:
            # 即使撤销失败，也继续更新状态
            pass

    # 更新任务状态为暂停
    task.status = "paused"
    task.celery_task_id = None  # 清除Celery任务ID
    db.commit()
    db.refresh(task)

    return {
        "id": task.id,
        "status": task.status,
        "progress": task.progress,
        "message": "Task processing paused"
    }


@router.post("/{task_id}/retry")
async def retry_task(task_id: int, db: Session = Depends(get_db)):
    """重试任务"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    # 检查任务状态
    if task.status != "failed":
        raise HTTPException(
            status_code=400,
            detail=f"Task cannot be retried. Current status: {task.status}"
        )
    # 检查是否有视频
    video_count = db.query(Video).filter(Video.task_id == task_id).count()
    if video_count == 0:
        raise HTTPException(
            status_code=400,
            detail="请先上传视频到任务"
        )
    # 重置任务状态
    task.status = "pending"
    task.progress = 0.0
    task.error_message = None
    task.celery_task_id = None  # 清除Celery任务ID
    db.commit()
    db.refresh(task)

    return {
        "id": task.id,
        "status": task.status,
        "progress": task.progress,
        "message": "Task retry started"
    }


@router.get("/{task_id}/logs")
async def get_task_logs(task_id: int, db: Session = Depends(get_db)):
    """获取任务处理日志"""
    from app.models.task import AnalysisResult

    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 获取所有相关视频的分析结果作为日志
    logs = []

    # 添加任务创建日志
    logs.append({
        "id": f"task_{task.id}_created",
        "level": "info",
        "message": f"任务 '{task.name}' 已创建",
        "timestamp": task.created_at.isoformat()
    })

    # 添加视频上传日志
    for video in task.videos:
        logs.append({
            "id": f"video_{video.id}_uploaded",
            "level": "info",
            "message": f"视频 '{video.original_filename}' 上传完成",
            "timestamp": video.created_at.isoformat()
        })

        # 添加分析结果日志
        analysis_results = db.query(AnalysisResult).filter(
            AnalysisResult.video_id == video.id
        ).order_by(AnalysisResult.created_at).all()

        for result in analysis_results:
            step_names = {
                "basic_info": "基础信息分析",
                "content_analysis": "内容分析",
                "plot_analysis": "情节分析"
            }
            step_name = step_names.get(result.step, result.step)

            logs.append({
                "id": f"analysis_{result.id}",
                "level": "success",
                "message": f"视频 '{video.original_filename}' {step_name}完成 (置信度: {result.confidence:.2f})",
                "timestamp": result.created_at.isoformat()
            })

    # 按时间倒序排序
    logs.sort(key=lambda x: x["timestamp"], reverse=True)

    return logs

