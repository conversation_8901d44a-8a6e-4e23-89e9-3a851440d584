#!/bin/bash

# 一键启动视频处理系统的所有服务
# 包括：前端开发服务器、后端API服务器、Celery工作进程

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="$SCRIPT_DIR/logs"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 颜色输出函数
print_info() {
    echo -e "\033[32m[INFO]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[33m[WARNING]\033[0m $1"
}

# 检查进程是否运行
check_process() {
    local name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0  # 进程正在运行
        else
            rm -f "$pid_file"  # 清理无效的PID文件
            return 1  # 进程未运行
        fi
    fi
    return 1  # PID文件不存在
}

# 启动前端服务
start_frontend() {
    print_info "启动前端服务..."
    
    cd "$SCRIPT_DIR/frontend"
    
    # 检查是否已经在运行
    if check_process "frontend" "$LOG_DIR/frontend.pid"; then
        print_warning "前端服务已经在运行中"
        return
    fi
    
    # 后台启动前端服务
    nohup bash start.sh > "$LOG_DIR/frontend.log" 2>&1 &
    local pid=$!
    echo $pid > "$LOG_DIR/frontend.pid"
    
    print_info "前端服务已启动，PID: $pid"
    print_info "日志文件: $LOG_DIR/frontend.log"
}

# 启动后端API服务
start_backend() {
    print_info "启动后端API服务..."
    
    cd "$SCRIPT_DIR/backend"
    
    # 检查是否已经在运行
    if check_process "backend" "$LOG_DIR/backend.pid"; then
        print_warning "后端API服务已经在运行中"
        return
    fi
    
    # 后台启动后端服务
    nohup bash main.sh > "$LOG_DIR/backend.log" 2>&1 &
    local pid=$!
    echo $pid > "$LOG_DIR/backend.pid"
    
    print_info "后端API服务已启动，PID: $pid"
    print_info "日志文件: $LOG_DIR/backend.log"
}

# 启动Celery工作进程
start_celery() {
    print_info "启动Celery工作进程..."
    
    cd "$SCRIPT_DIR/backend"
    
    # 检查是否已经在运行
    if check_process "celery" "$LOG_DIR/celery.pid"; then
        print_warning "Celery工作进程已经在运行中"
        return
    fi
    
    # 后台启动Celery工作进程
    nohup bash celery_worker.sh > "$LOG_DIR/celery.log" 2>&1 &
    local pid=$!
    echo $pid > "$LOG_DIR/celery.pid"
    
    print_info "Celery工作进程已启动，PID: $pid"
    print_info "日志文件: $LOG_DIR/celery.log"
}

# 停止所有服务
stop_all() {
    print_info "停止所有服务..."
    
    # 停止前端服务
    if check_process "frontend" "$LOG_DIR/frontend.pid"; then
        local pid=$(cat "$LOG_DIR/frontend.pid")
        kill $pid 2>/dev/null
        rm -f "$LOG_DIR/frontend.pid"
        print_info "前端服务已停止"
    fi
    
    # 停止后端服务
    if check_process "backend" "$LOG_DIR/backend.pid"; then
        local pid=$(cat "$LOG_DIR/backend.pid")
        kill $pid 2>/dev/null
        rm -f "$LOG_DIR/backend.pid"
        print_info "后端API服务已停止"
    fi
    
    # 停止Celery工作进程
    if check_process "celery" "$LOG_DIR/celery.pid"; then
        local pid=$(cat "$LOG_DIR/celery.pid")
        kill $pid 2>/dev/null
        rm -f "$LOG_DIR/celery.pid"
        print_info "Celery工作进程已停止"
    fi
}

# 查看服务状态
status() {
    print_info "检查服务状态..."
    
    echo "----------------------------------------"
    
    # 检查前端服务
    if check_process "frontend" "$LOG_DIR/frontend.pid"; then
        local pid=$(cat "$LOG_DIR/frontend.pid")
        echo "前端服务: 运行中 (PID: $pid)"
    else
        echo "前端服务: 未运行"
    fi
    
    # 检查后端服务
    if check_process "backend" "$LOG_DIR/backend.pid"; then
        local pid=$(cat "$LOG_DIR/backend.pid")
        echo "后端API服务: 运行中 (PID: $pid)"
    else
        echo "后端API服务: 未运行"
    fi
    
    # 检查Celery工作进程
    if check_process "celery" "$LOG_DIR/celery.pid"; then
        local pid=$(cat "$LOG_DIR/celery.pid")
        echo "Celery工作进程: 运行中 (PID: $pid)"
    else
        echo "Celery工作进程: 未运行"
    fi
    
    echo "----------------------------------------"
}

# 查看日志
logs() {
    local service=$1
    
    case $service in
        "frontend")
            if [ -f "$LOG_DIR/frontend.log" ]; then
                tail -f "$LOG_DIR/frontend.log"
            else
                print_error "前端日志文件不存在"
            fi
            ;;
        "backend")
            if [ -f "$LOG_DIR/backend.log" ]; then
                tail -f "$LOG_DIR/backend.log"
            else
                print_error "后端日志文件不存在"
            fi
            ;;
        "celery")
            if [ -f "$LOG_DIR/celery.log" ]; then
                tail -f "$LOG_DIR/celery.log"
            else
                print_error "Celery日志文件不存在"
            fi
            ;;
        *)
            print_error "未知的服务名称: $service"
            print_info "可用的服务: frontend, backend, celery"
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "视频处理系统一键启动脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动所有服务 (默认)"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  status    查看服务状态"
    echo "  logs      查看指定服务日志"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 启动所有服务"
    echo "  $0 start              # 启动所有服务"
    echo "  $0 stop               # 停止所有服务"
    echo "  $0 status             # 查看服务状态"
    echo "  $0 logs frontend      # 查看前端日志"
    echo "  $0 logs backend       # 查看后端日志"
    echo "  $0 logs celery        # 查看Celery日志"
}

# 主函数
main() {
    case ${1:-start} in
        "start")
            print_info "开始启动所有服务..."
            start_frontend
            sleep 2
            start_backend
            sleep 2
            start_celery
            echo ""
            status
            print_info "所有服务启动完成！"
            print_info "使用 '$0 status' 查看服务状态"
            print_info "使用 '$0 logs <service>' 查看服务日志"
            ;;
        "stop")
            stop_all
            ;;
        "restart")
            stop_all
            sleep 3
            print_info "重新启动所有服务..."
            start_frontend
            sleep 2
            start_backend
            sleep 2
            start_celery
            echo ""
            status
            ;;
        "status")
            status
            ;;
        "logs")
            if [ -z "$2" ]; then
                print_error "请指定要查看日志的服务名称"
                print_info "可用的服务: frontend, backend, celery"
            else
                logs "$2"
            fi
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
